package ir.rahavardit.ariel.ui.users

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.UserListItem
import ir.rahavardit.ariel.databinding.ItemUserBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Adapter for displaying users in a RecyclerView.
 *
 * @property viewModel The ViewModel to use for fetching additional user data.
 * @property lifecycleOwner The LifecycleOwner to use for coroutine scope.
 */
class UserAdapter(
    private val viewModel: UsersViewModel,
    private val lifecycleOwner: LifecycleOwner
) : ListAdapter<UserListItem, UserAdapter.UserViewHolder>(UserDiffCallback()) {

    private val TAG = "UserAdapter"

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserViewHolder {
        val binding = ItemUserBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return UserViewHolder(binding)
    }

    override fun onBindViewHolder(holder: UserViewHolder, position: Int) {
        holder.bind(getItem(position))
    }



    /**
     * ViewHolder for a user item.
     */
    inner class UserViewHolder(private val binding: ItemUserBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val sessionManager = SessionManager(binding.root.context)

        /**
         * Binds a user to the ViewHolder.
         *
         * @param user The user to bind.
         */
        fun bind(user: UserListItem) {
            binding.apply {
                // Set avatar with first letter of username
                tvAvatar.text = user.username.firstOrNull()?.uppercase() ?: "?"

                // Display username without label
                tvUsername.text = user.username

                // Display full name without label
                val fullName = "${user.firstName} ${user.lastName}".trim()
                tvFullName.text = if (fullName.isNotBlank()) fullName else binding.root.context.getString(R.string.not_specified)

                // Display email without label
                tvEmail.text = if (user.email.isNotBlank()) user.email else binding.root.context.getString(R.string.not_specified)

                // Show admin status chip if user is admin
                chipAdminStatus.visibility = if (user.isSuperuser || user.isLimitedAdmin) {
                    android.view.View.VISIBLE
                } else {
                    android.view.View.GONE
                }

                // Show company if available
                if (!user.company.isNullOrBlank()) {
                    tvCompany.text = user.company
                    tvCompany.visibility = android.view.View.VISIBLE
                } else {
                    tvCompany.visibility = android.view.View.GONE
                }
            }
        }


    }

    /**
     * DiffUtil callback for comparing users.
     */
    private class UserDiffCallback : DiffUtil.ItemCallback<UserListItem>() {
        override fun areItemsTheSame(oldItem: UserListItem, newItem: UserListItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: UserListItem, newItem: UserListItem): Boolean {
            return oldItem == newItem
        }
    }
}
