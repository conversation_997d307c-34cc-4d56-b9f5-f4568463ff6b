<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- User Avatar -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_avatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            app:cardCornerRadius="24dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="?attr/colorPrimary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:textColor="?attr/colorOnPrimary"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="A" />

        </com.google.android.material.card.MaterialCardView>

        <!-- Username -->
        <TextView
            android:id="@+id/tv_username"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/chip_admin_status"
            app:layout_constraintStart_toEndOf="@id/card_avatar"
            app:layout_constraintTop_toTopOf="@id/card_avatar"
            tools:text="admin" />

        <!-- Admin Status Chip -->
        <com.google.android.material.chip.Chip
            android:id="@+id/chip_admin_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/users__admin"
            android:textSize="12sp"
            android:visibility="gone"
            app:chipBackgroundColor="?attr/colorSecondary"
            app:chipCornerRadius="12dp"
            app:chipMinHeight="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/card_avatar"
            tools:visibility="visible" />

        <!-- Full Name -->
        <TextView
            android:id="@+id/tv_full_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?attr/colorOnSurfaceVariant"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/card_avatar"
            app:layout_constraintTop_toBottomOf="@id/tv_username"
            tools:text="ادمین ادمینی" />

        <!-- Email -->
        <TextView
            android:id="@+id/tv_email"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?attr/colorOnSurfaceVariant"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/card_avatar"
            tools:text="<EMAIL>" />

        <!-- Company/Position (optional) -->
        <TextView
            android:id="@+id/tv_company"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_email"
            tools:text="My Company"
            tools:visibility="visible" />

        <!-- Hidden fields for compatibility -->
        <TextView
            android:id="@+id/tv_position"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_gender"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_categories"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_is_superuser"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_is_limited_admin"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_groups"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_short_uuid"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
